<?php

declare(strict_types=1);

namespace App\GlApi\Jobs;

use App\Common\Facades\Mediator;
use App\Enquiry\Exceptions\EnquiryAlreadyExists;
use App\Enquiry\UseCases\CreateEnquiry\Contact;
use App\Enquiry\UseCases\CreateEnquiry\CreateEnquiry;
use App\Enquiry\UseCases\UpdateEnquiry\UpdateEnquiry;
use App\GlApi\Enums\RequestStatus;
use App\GlApi\Models\ApiRequest;
use App\Modules\Facebook\Jobs\RecordEnquiry\PhoneNumber;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Shared\ValueObjects\PhoneNumber as VOPhoneNumber;
use Throwable;

final class ProcessLead implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public function __construct(
        private readonly int $apiRequestId
    )
    {
        $this->onQueue('integration:api');
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array<int, object>
     */
    public function middleware(): array
    {
        $apiRequest = ApiRequest::query()->find($this->apiRequestId);

        if (!$apiRequest) {
            return [];
        }

        // Use vendor_id and phone_number combination as the key
        $key = "vendor:{$apiRequest->vendor_id}:phone:{$apiRequest->phone_number}";

        return [
            (new WithoutOverlapping($key))
                ->dontRelease() // Don't release the job back to queue if overlapping
                ->expireAfter(300) // Expire lock after 5 minutes
        ];
    }

    public function handle(): void
    {
        $apiRequest = ApiRequest::query()->find($this->apiRequestId);

        if (!$apiRequest instanceof ApiRequest) {
            Log::error('API request not found', [
                'api_request_id' => $this->apiRequestId,
            ]);
            return;
        }

        Log::info('Processing lead from API request', [
            'api_request_id' => $apiRequest->id,
            'vendor_id' => $apiRequest->vendor_id,
        ]);

        $payload = $apiRequest->payload;

        $phoneNumber = new VOPhoneNumber($apiRequest->phone_number);

        $contact = new Contact(
            name: $apiRequest->name,
            phoneNumber: new PhoneNumber(
                nationalNumber: $phoneNumber->nationalNumber(),
                countryCode: (string)$phoneNumber->countryCode()
            ),
            email: $payload['email'] ?? null
        );

        $metadata = [
            'feedback' => $payload['feedback'] ?? null,
            'company_name' => $payload['company_name'] ?? null,
            'status' => $payload['status'] ?? null,
            'type' => $payload['type'] ?? null,
            'purpose' => $payload['purpose'] ?? null,
            'staff_name' => $payload['staff_name'] ?? null,
            'notes' => $payload['lead_note'] ?? null,
            'department' => $payload['department'] ?? null,
            'more_phone_numbers' => $payload['more_phone_numbers'] ?? null,
            'agency' => $payload['agency'] ?? null,
            ...Arr::except($payload, [ //for additional fields
                'feedback',
                'company_name',
                'status',
                'type',
                'purpose',
                'staff_name',
                'notes',
                'department',
                'more_phone_numbers',
                'agency',
            ]),
        ];

        try {
            Mediator::dispatch(new CreateEnquiry(
                vendorId: $apiRequest->vendor_id,
                source: $payload['source'] ?? 'Gl Api',
                contact: $contact,
                createdBy: $apiRequest->by,
                metadata: $metadata
            ));

        } catch (EnquiryAlreadyExists) {
            Log::info('Contact already exists', [
                'api_request_id' => $this->apiRequestId,
            ]);

            Mediator::dispatch(new UpdateEnquiry(
                vendorId: $apiRequest->vendor_id,
                source: $payload['source'] ?? 'Gl Api',
                contact: $contact,
                metadata: $metadata,
            ));
        }

        $apiRequest->update([
            'status' => RequestStatus::Completed,
        ]);

        Log::info('Successfully processed lead from API request', [
            'api_request_id' => $apiRequest->id,
            'vendor_id' => $apiRequest->vendor_id,
        ]);
    }

    public function failed(Throwable $exception): void
    {
        Log::error('Processing lead failed- came via API', [
            'request_id' => $this->apiRequestId,
            'reason' => $exception->getMessage(),
        ]);

        ApiRequest::query()
            ->where('id', '=', $this->apiRequestId)
            ->update([
                'status' => RequestStatus::Failed,
                'failure_reason' => Str::limit($exception->getMessage(), 200),
            ]);
    }
}

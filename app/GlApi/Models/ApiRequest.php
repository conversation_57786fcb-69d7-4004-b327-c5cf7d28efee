<?php

declare(strict_types=1);

namespace App\GlApi\Models;

use App\Common\Unguarded;
use App\GlApi\Enums\RequestStatus;
use App\GlApi\Enums\RequestType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $vendor_id
 * @property string $name
 * @property string $phone_number
 * @property array $payload
 * @property RequestStatus $status
 * @property int $by
 * @property RequestType $type
 * @property string|null $failure_reason
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereFailureReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest wherePhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiRequest whereVendorId($value)
 * @mixin \Eloquent
 */
final class ApiRequest extends Model
{
    use Unguarded;
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'gl_api_requests';

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'payload' => 'array',
        'status' => RequestStatus::class,
        'type' => RequestType::class,
    ];
}

<?php

declare(strict_types=1);

namespace App\GlApi\Http\Controllers;

use App\GlApi\Enums\RequestStatus;
use App\GlApi\Enums\RequestType;
use App\GlApi\Http\Requests\CreateLeadRequest;
use App\GlApi\Http\Resources\ApiResponse;
use App\GlApi\Http\Traits\HasGlApiAuthentication;
use App\GlApi\Jobs\ProcessLead;
use App\GlApi\Models\ApiRequest;
use App\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

final class CreateLeadController
{
    use HasGlApiAuthentication;

    /**
     * Handle both GET and POST requests for lead creation
     */
    public function __invoke(CreateLeadRequest $request): JsonResponse
    {
        Log::info('Contact via GL connect api received - new', [
            'request' => $request->all(),
            'method' => $request->method(),
        ]);

        try {
            $userId = $this->getApiUserId($request);
            $vendorId = User::getVendorIdApi($userId);

            $request = ApiRequest::query()
                ->create([
                    'vendor_id' => $vendorId,
                    'by' => $userId,
                    'name' => $request->string('name')->toString(),
                    'phone_number' => (string)$request->parsePhoneNumber(),
                    'status' => RequestStatus::Pending,
                    'type' => RequestType::NewLead,
                    'payload' => $request->except(['name', 'mobileno', 'token', 'countrycode', 'api_user_id']),
                    'created_at' => now(),
                ]);

            Bus::dispatch(new ProcessLead(apiRequestId: $request->id));

            return ApiResponse::success('Lead creation request accepted', [], 202);

        } catch (Exception $e) {
            Log::error('Failed to create lead via API', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'method' => $request->method(),
                'data' => $request->all(),
            ]);

            return ApiResponse::error('Failed to create lead: ' . $e->getMessage());
        }
    }
}

<?php

declare(strict_types=1);

namespace App\GlApi\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Shared\Exceptions\PhoneNumberIsInvalid;
use Shared\ValueObjects\PhoneNumber;

final class CreateLeadRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required',
            'email' => 'nullable|email',
            'mobileno' => 'required',
        ];
    }

    public function parsePhoneNumber(): PhoneNumber
    {
        $number = $this->string('mobileno');

        try {
            return new PhoneNumber($number->start('+')->value());
        } catch (PhoneNumberIsInvalid) {
            try {
                return new PhoneNumber($this->string('countrycode', '91')->append($number->ltrim('+'))->start(
                    '+'
                )->value());
            } catch (PhoneNumberIsInvalid) {
                throw PhoneNumberIsInvalid::forNumber($number->value());
            }
        }
    }

    /**
     * Prepare the data for validation.
     * Handle both mobileno and mobile_no field names by normalizing to mobileno.
     */
    protected function prepareForValidation(): void
    {
        // If mobile_no is provided but mobileno is not, copy mobile_no to mobileno
        if ($this->has('mobile_no')
            && ! $this->has('mobileno')) {
            $this->merge([
                'mobileno' => $this->get('mobile_no'),
            ]);
        }

        // Clean the mobile number by keeping only digits and leading + sign
        if (! $this->has('mobileno') || $this->get('mobileno') === null) {
            return;
        }

        $this->merge([
            'mobileno' => $this->string('mobileno')
                ->replaceMatches('/[^0-9]/', '')
                ->value(),
        ]);
    }
}

<?php

namespace App\Deal\UseCases\GetDealStats;

use DateInterval;
use Shared\Mediator\Contracts\Cacheable;
use Shared\Mediator\Request;

/**
 * @implements Request<DealStats>
 * @see GetDealStatsHandler
 */
final class GetDealStats implements Request, Cacheable
{
    public function __construct(
        public readonly int $vendorId,
        public readonly ?int $agentId = null,
    ) {
    }

    public function cacheKey(): string
    {
        return 'get.deals.stats.' . json_encode($this->toArray());
    }

    public function ttl(): DateInterval
    {
        return new DateInterval('PT5M');
    }

    public function toArray(): array
    {
        return [
            'vendor_id' => $this->vendorId,
            'agent_id' => $this->agentId,
        ];
    }
}
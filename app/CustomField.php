<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class CustomField extends Model
{
    public static function validations($userId, $type, $rule)
    {
        $custom_fields = CustomField::where('user_id', User::getVendorIdApi($userId))->where('type', $type)->get();
        foreach ($custom_fields as $custom_field) {
            $validations = [];
            if ($custom_field->is_required == 1)
                array_push($validations, 'required');
            if ($custom_field->field_type == 'email')
                array_push($validations, 'email');
            if (substr($custom_field->field_type, 0, 4) == 'file')
                array_push($validations, explode('|', $custom_field->field_type)[1]);
            $rule[$custom_field->field_name] = $validations;
        }
        return $rule;
    }
}

<?php

declare(strict_types=1);

namespace App\Enquiry\Jobs;

use App\Enquiry\Enums\ImportContactStatus;
use App\Enquiry\Enums\ImportRequestStatus;
use App\Enquiry\Events\ImportRequestCompleted;
use App\Enquiry\Models\ImportContact;
use App\Enquiry\Models\ImportRequest;
use Illuminate\Bus\Batch;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\LazyCollection;
use Throwable;

final class SanitizeImport implements ShouldQueue
{
    use InteractsWithQueue;

    public string $queue = 'lead-import';

    public int $timeout = 3600;

    public function __construct(
        public readonly int $importRequestId,
        public readonly int $vendorId,
        public readonly int $chunkSize = 1000,
    ) {
    }

    /**
     * @return object[]
     */
    public function middleware(): array
    {
        return [(new WithoutOverlapping((string) $this->importRequestId))];
    }

    public function handle(): void
    {
        Log::withContext([
            'vendor_id' => $this->vendorId,
            'import_request_id' => $this->importRequestId,
        ])->info('Sanitizing import');

        $importRequest = $this->getImportRequest(
            importRequestId: $this->importRequestId
        );

        if (! $importRequest instanceof ImportRequest) {
            Log::error('Import request not found', [
                'vendor_id' => $this->vendorId,
                'import_request_id' => $this->importRequestId,
            ]);
            return;
        }

        $this->markDuplicatePhoneNumbers();

        Log::info('Import sanitization completed');
    }

    private function markDuplicatePhoneNumbers(): void
    {
        Log::info('Starting duplicate phone number detection', [
            'import_request_id' => $this->importRequestId,
        ]);

        // Find all phone numbers that appear more than once for this import request
        $duplicatePhoneNumbers = ImportContact::query()
            ->where('import_request_id', $this->importRequestId)
            ->where('status', ImportContactStatus::Pending)
            ->selectRaw('phone_number, COUNT(*) as count')
            ->groupBy('phone_number')
            ->having('count', '>', 1)
            ->pluck('phone_number')
            ->toArray();

        if (empty($duplicatePhoneNumbers)) {
            Log::info('No duplicate phone numbers found');
            return;
        }

        Log::info('Found duplicate phone numbers', [
            'count' => count($duplicatePhoneNumbers),
            'phone_numbers' => $duplicatePhoneNumbers,
        ]);

        // For each duplicate phone number, keep the first one and mark others as duplicate
        foreach ($duplicatePhoneNumbers as $phoneNumber) {
            $contacts = ImportContact::query()
                ->where('import_request_id', $this->importRequestId)
                ->where('phone_number', $phoneNumber)
                ->where('status', ImportContactStatus::Pending)
                ->orderBy('id')
                ->get();

            // Skip the first contact (keep it as unique) and mark the rest as duplicates
            $contactsToMarkAsDuplicate = $contacts->skip(1);

            foreach ($contactsToMarkAsDuplicate as $contact) {
                $contact->update([
                    'status' => ImportContactStatus::Duplicate,
                    'reason' => 'Duplicate phone number: ' . $phoneNumber,
                ]);
            }

            Log::info('Marked contacts as duplicate', [
                'phone_number' => $phoneNumber,
                'total_contacts' => $contacts->count(),
                'marked_as_duplicate' => $contactsToMarkAsDuplicate->count(),
                'kept_unique' => 1,
            ]);
        }

        $totalMarkedAsDuplicate = ImportContact::query()
            ->where('import_request_id', $this->importRequestId)
            ->where('status', ImportContactStatus::Duplicate)
            ->count();

        Log::info('Duplicate phone number detection completed', [
            'total_marked_as_duplicate' => $totalMarkedAsDuplicate,
        ]);
    }

    private function getImportRequest(int $importRequestId): ?ImportRequest
    {
        return ImportRequest::query()
            ->where('id', '=', $importRequestId)
            ->where('status', '=', ImportRequestStatus::InProgress)
            ->first();
    }
}

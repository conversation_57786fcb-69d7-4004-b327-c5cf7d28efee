<?php

declare(strict_types=1);

namespace App\Enquiry\Jobs;

use App\Enquiry\Enums\ImportContactStatus;
use App\Enquiry\Enums\ImportRequestStatus;
use App\Enquiry\Events\ImportRequestCompleted;
use App\Enquiry\Models\ImportContact;
use App\Enquiry\Models\ImportRequest;
use Illuminate\Bus\Batch;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\LazyCollection;
use Throwable;

final class SanitizeImport implements ShouldQueue
{
    use InteractsWithQueue;

    public string $queue = 'lead-import';

    public int $timeout = 3600;

    public function __construct(
        public readonly int $importRequestId,
        public readonly int $vendorId,
        public readonly int $chunkSize = 1000,
    ) {
    }

    /**
     * @return object[]
     */
    public function middleware(): array
    {
        return [(new WithoutOverlapping((string) $this->importRequestId))];
    }

    public function handle(): void
    {
        Log::withContext([
            'vendor_id' => $this->vendorId,
            'import_request_id' => $this->importRequestId,
        ])->info('Sanitizing import');

        $importRequest = $this->getImportRequest(
            importRequestId: $this->importRequestId
        );

        if (! $importRequest instanceof ImportRequest) {
            Log::error('Import request not found', [
                'vendor_id' => $this->vendorId,
                'import_request_id' => $this->importRequestId,
            ]);
            return;
        }

        Log::info('All import batches dispatched');
    }

    private function getImportRequest(int $importRequestId): ?ImportRequest
    {
        return ImportRequest::query()
            ->where('id', '=', $importRequestId)
            ->where('status', '=', ImportRequestStatus::InProgress)
            ->first();
    }
}

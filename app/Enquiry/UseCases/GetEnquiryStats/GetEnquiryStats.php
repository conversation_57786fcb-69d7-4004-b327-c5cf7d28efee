<?php

namespace App\Enquiry\UseCases\GetEnquiryStats;

use Carbon\Carbon;
use DateInterval;
use Shared\Mediator\Contracts\Cacheable;
use Shared\Mediator\Request;

/**
 * @implements Request<EnquiryStats>
 * @see GetEnquiryStatsHandler
 */
final class GetEnquiryStats implements Request, Cacheable
{
    public function __construct(
        public readonly int $vendorId,
        public readonly Carbon $date,
        public readonly ?int $agentId = null,
    ) {
    }

    public function cacheKey(): string
    {
        return 'get.enquiries.stats.' . json_encode($this->toArray());
    }

    public function ttl(): DateInterval
    {
        return new DateInterval('PT5M');
    }

    private function toArray(): array
    {
        return [
            'vendor_id' => $this->vendorId,
            'date' => $this->date->toDateString(),
            'agent_id' => $this->agentId,
        ];
    }
}
<?php

declare(strict_types=1);

namespace Tests\Feature\GlApi;

use App\GlApi\Enums\RequestStatus;
use App\GlApi\Enums\RequestType;
use App\GlApi\Models\ApiRequest;
use App\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Testing\TestResponse;
use Tests\TestCase;

class CreateLeadControllerTest extends TestCase
{
    use DatabaseTransactions;

    private const API_ENDPOINT = '/api/gl-website-contacts';

    protected function setUp(): void
    {
        parent::setUp();

        // Disable middleware for testing
        $this->withoutMiddleware();
    }

    /**
     * @test
     */
    public function it_can_create_lead_via_post_request(): void
    {
        $data = [
            'name' => 'John Doe',
            'mobileno' => '919876543210',
            'email' => '<EMAIL>',
            'requirement' => 'Looking for a CRM solution',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Lead created successfully',
            ]);

        // Verify the API request was created
        $this->assertDatabaseHas('gl_api_requests', [
            'name' => 'John Doe',
            'phone_number' => '+919876543210',
            'status' => RequestStatus::Pending->value,
            'type' => RequestType::NewLead->value,
        ]);

        // Verify payload was stored correctly
        $apiRequest = ApiRequest::query()->latest()->first();
        $payload = $apiRequest->payload;
        $this->assertEquals('<EMAIL>', $payload['email']);
        $this->assertEquals('Looking for a CRM solution', $payload['requirement']);
        $this->assertEquals('Mumbai', $payload['city']);
    }

    /**
     * @test
     */
    public function it_can_create_lead_via_get_request(): void
    {
        $data = [
            'name' => 'Jane Smith',
            'mobileno' => '918765432109',
            'email' => '<EMAIL>',
        ];

        $response = $this->requestWithApiUser('GET', $data);

        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Lead created successfully',
            ]);

        // Verify the API request was created
        $this->assertDatabaseHas('gl_api_requests', [
            'name' => 'Jane Smith',
            'phone_number' => '+918765432109',
            'status' => RequestStatus::Pending->value,
            'type' => RequestType::NewLead->value,
        ]);
    }

    /**
     * @test
     */
    public function it_handles_mobile_no_field_name_variation(): void
    {
        // Test with 'mobile_no' instead of 'mobileno'
        $data = [
            'name' => 'Test User',
            'mobile_no' => '7654321098',
            'email' => '<EMAIL>',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Lead created successfully',
            ]);

        $this->assertDatabaseHas('gl_api_requests', [
            'name' => 'Test User',
            'phone_number' => '+917654321098',
        ]);
    }

    /**
     * @test
     */
    public function it_validates_required_fields(): void
    {
        // Missing name
        $response = $this->requestWithApiUser('POST', [
            'mobileno' => '9876543210',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name']);

        // Missing mobile number
        $response = $this->requestWithApiUser('POST', [
            'name' => 'John Doe',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['mobileno']);
    }

    /**
     * @test
     */
    public function it_validates_email_format(): void
    {
        $data = [
            'name' => 'John Doe',
            'mobileno' => '9876543210',
            'email' => 'invalid-email',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /**
     * @test
     */
    public function it_handles_phone_numbers_with_country_code(): void
    {
        $data = [
            'name' => 'International User',
            'mobileno' => '+919876543210',
            'email' => '<EMAIL>',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertOk();

        $this->assertDatabaseHas('gl_api_requests', [
            'name' => 'International User',
            'phone_number' => '+919876543210',
        ]);
    }

    /**
     * @test
     */
    public function it_handles_phone_numbers_with_custom_country_code(): void
    {
        $data = [
            'name' => 'US User',
            'mobileno' => '9847505250',
            'countrycode' => '91',
            'email' => '<EMAIL>',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertOk();

        $this->assertDatabaseHas('gl_api_requests', [
            'name' => 'US User',
            'phone_number' => '+919847505250',
        ]);
    }

    /**
     * @test
     */
    public function it_cleans_phone_number_special_characters(): void
    {
        $data = [
            'name' => 'Test User',
            'mobileno' => '(*************',
            'email' => '<EMAIL>',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertOk();

        $this->assertDatabaseHas('gl_api_requests', [
            'name' => 'Test User',
            'phone_number' => '+919876543210',
        ]);
    }

    /**
     * @test
     */
    public function it_stores_additional_fields_in_payload(): void
    {
        $data = [
            'name' => 'Detailed Lead',
            'mobileno' => '9876543210',
            'email' => '<EMAIL>',
            'requirement' => 'CRM with API integration',
            'budget' => '50000',
            'company_name' => 'Tech Corp',
            'designation' => 'Manager',
            'city' => 'Bangalore',
            'state' => 'Karnataka',
            'source' => 'Website',
            'remarks' => 'Urgent requirement',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertOk();

        $apiRequest = ApiRequest::query()->where('name', 'Detailed Lead')->first();
        $payload = $apiRequest->payload;

        $this->assertEquals('<EMAIL>', $payload['email']);
        $this->assertEquals('CRM with API integration', $payload['requirement']);
        $this->assertEquals('50000', $payload['budget']);
        $this->assertEquals('Tech Corp', $payload['company_name']);
        $this->assertEquals('Bangalore', $payload['city']);
        $this->assertEquals('Website', $payload['source']);
    }

    /**
     * @test
     */
    public function it_returns_error_when_api_user_id_is_missing(): void
    {
        // Enable middleware for this test
        $this->withMiddleware();

        $response = $this->postJson(self::API_ENDPOINT, [
            'name' => 'John Doe',
            'mobileno' => '9876543210',
        ]);

        // Should fail due to missing authentication
        $response->assertStatus(401);
    }

    /**
     * @test
     */
    public function it_excludes_sensitive_fields_from_payload(): void
    {
        $data = [
            'name' => 'Security Test',
            'mobileno' => '9876543210',
            'token' => 'secret-token',
            'countrycode' => '91',
            'email' => '<EMAIL>',
        ];

        $response = $this->requestWithApiUser('POST', $data);

        $response->assertOk();

        $apiRequest = ApiRequest::query()->where('name', 'Security Test')->first();
        $payload = $apiRequest->payload;

        $this->assertArrayNotHasKey('name', $payload);
        $this->assertArrayNotHasKey('mobile_no', $payload);
        $this->assertArrayNotHasKey('mobileno', $payload);
        $this->assertArrayNotHasKey('token', $payload);
        $this->assertArrayNotHasKey('countrycode', $payload);

        // But email should be present
        $this->assertArrayHasKey('email', $payload);
    }

    /**
     * Helper method to make a request with an authenticated API user
     */
    private function requestWithApiUser(
        string $method,
        array $data = []
    ): TestResponse {
        // Create vendor user
        $user = User::factory()->create([
            'parent_user_id' => 2,
        ]);

        // Simulate middleware behavior by adding api_user_id
        if ($method === 'GET') {
            return $this->getJson(self::API_ENDPOINT . '?' . http_build_query([
                ...$data,
                'api_user_id' => $user->pk_int_user_id,
            ]), );
        }

        return $this->postJson(self::API_ENDPOINT, [
            ...$data,
            'api_user_id' => $user->pk_int_user_id,
        ]);
    }
}

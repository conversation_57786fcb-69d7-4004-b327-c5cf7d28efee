<?php

declare(strict_types=1);

namespace Tests\Unit\GlApi\Jobs;

use App\Common\Facades\Mediator;
use App\Enquiry\Exceptions\EnquiryAlreadyExists;
use App\Enquiry\UseCases\CreateEnquiry\Contact;
use App\Enquiry\UseCases\CreateEnquiry\CreateEnquiry;
use App\Enquiry\UseCases\UpdateEnquiry\UpdateEnquiry;
use App\GlApi\Enums\RequestStatus;
use App\GlApi\Jobs\ProcessLead;
use App\GlApi\Models\ApiRequest;
use Exception;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

final class ProcessLeadTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        Mediator::fake([CreateEnquiry::class, UpdateEnquiry::class]);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }

    /**
     * @test
     */
    public function it_should_process_lead_successfully_when_api_request_exists(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'John Doe',
            'phone_number' => '+971551234567',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [
                'email' => '<EMAIL>',
                'feedback' => 'Test feedback',
                'company_name' => 'Test Company',
                'status' => 'New',
                'type' => 'Hot Lead',
                'purpose' => 'Product Inquiry',
                'staff_name' => 'Staff Member',
                'lead_note' => 'This is a test note',
                'department' => 'Sales',
                'more_phone_numbers' => '+971552345678',
                'agency' => 'test-agency',
                'source' => 'Website',
                'custom_field' => 'custom_value',
            ],
        ]);

        Log::shouldReceive('info')->times(2);

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->handle();

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Completed, $apiRequest->status);

        Mediator::assertDispatched(
            CreateEnquiry::class,
            static fn (CreateEnquiry $command) => $command->vendorId === $apiRequest->vendor_id
                && $command->source === 'Website'
                && $command->contact->name === 'John Doe'
                && $command->contact->phoneNumber->toPhoneNumber() === '971551234567'
                && $command->contact->email === '<EMAIL>'
                && $command->createdBy === 1
                && $command->metadata['feedback'] === 'Test feedback'
                && $command->metadata['company_name'] === 'Test Company'
                && $command->metadata['status'] === 'New'
                && $command->metadata['type'] === 'Hot Lead'
                && $command->metadata['purpose'] === 'Product Inquiry'
                && $command->metadata['staff_name'] === 'Staff Member'
                && $command->metadata['notes'] === 'This is a test note'
                && $command->metadata['department'] === 'Sales'
                && $command->metadata['more_phone_numbers'] === '+971552345678'
                && $command->metadata['agency'] === 'test-agency'
                && $command->metadata['custom_field'] === 'custom_value'
        );
    }

    /**
     * @test
     */
    public function it_should_use_default_source_when_source_not_provided(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'Jane Doe',
            'phone_number' => '+971559876543',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [
                'email' => '<EMAIL>',
            ],
        ]);

        Log::shouldReceive('info')->times(2);

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->handle();

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Completed, $apiRequest->status);

        Mediator::assertDispatched(
            CreateEnquiry::class,
            static fn (CreateEnquiry $command) => $command->source === 'Gl Api'
        );
    }

    /**
     * @test
     */
    public function it_should_update_enquiry_when_enquiry_already_exists(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'Existing User',
            'phone_number' => '+971557777777',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [
                'email' => '<EMAIL>',
                'feedback' => 'Updated feedback',
            ],
        ]);

        // Re-configure the fake to throw exception for CreateEnquiry
        Mediator::swap(Mediator::fake([
            CreateEnquiry::class => static function (): void {
                throw EnquiryAlreadyExists::withPhoneNumber('971557777777', 1);
            },
            UpdateEnquiry::class,
        ]));

        Log::shouldReceive('info')->times(3); // Extra log for "Contact already exists"

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->handle();

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Completed, $apiRequest->status);

        Mediator::assertDispatched(CreateEnquiry::class);
        Mediator::assertDispatched(
            UpdateEnquiry::class,
            static fn (UpdateEnquiry $command) => $command->vendorId === $apiRequest->vendor_id
                && $command->source === 'Gl Api'
                && $command->contact->name === 'Existing User'
                && $command->contact->phoneNumber->toPhoneNumber() === '971557777777'
                && $command->metadata['feedback'] === 'Updated feedback'
        );
    }

    /**
     * @test
     */
    public function it_should_log_error_when_api_request_not_found(): void
    {
        // Arrange
        $nonExistentId = 999999;

        Log::shouldReceive('error')
            ->once()
            ->with('API request not found', [
                'api_request_id' => $nonExistentId,
            ]);

        // Act
        $job = new ProcessLead($nonExistentId);
        $job->handle();

        // Assert - no exception should be thrown
        $this->assertTrue(true);

        Mediator::assertNotDispatched(CreateEnquiry::class);
        Mediator::assertNotDispatched(UpdateEnquiry::class);
    }

    /**
     * @test
     */
    public function it_should_handle_failed_job_and_update_status(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'Failed User',
            'phone_number' => '+971556666666',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [],
        ]);

        $exception = new Exception('Something went wrong during processing');

        Log::shouldReceive('error')
            ->once()
            ->with('Processing lead failed- came via API', [
                'request_id' => $apiRequest->id,
                'reason' => 'Something went wrong during processing',
            ]);

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->failed($exception);

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Failed, $apiRequest->status);
        $this->assertEquals('Something went wrong during processing', $apiRequest->failure_reason);
    }

    /**
     * @test
     */
    public function it_should_truncate_long_failure_reason(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'Failed User',
            'phone_number' => '+971558888888',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [],
        ]);

        // Create a message that will definitely be longer than 200 chars
        $longMessage = str_repeat('A', 300);
        $exception = new Exception($longMessage);

        Log::shouldReceive('error')->once();

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->failed($exception);

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Failed, $apiRequest->status);
        // Check that it was actually truncated (original is 300 chars)
        $this->assertLessThan(strlen($longMessage), strlen($apiRequest->failure_reason));
        // The failure reason should not be the full 300 character message
        $this->assertNotEquals($longMessage, $apiRequest->failure_reason);
    }

    /**
     * @test
     */
    public function it_should_handle_null_values_in_payload_gracefully(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'Test User',
            'phone_number' => '+971555555555',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [
                'email' => null,
                'feedback' => null,
                'company_name' => null,
                'status' => null,
                'type' => null,
                'purpose' => null,
                'staff_name' => null,
                'lead_note' => null,
                'department' => null,
                'more_phone_numbers' => null,
                'agency' => null,
            ],
        ]);

        Log::shouldReceive('info')->times(2);

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->handle();

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Completed, $apiRequest->status);

        Mediator::assertDispatched(
            CreateEnquiry::class,
            static fn (CreateEnquiry $command) => $command->contact->email === null
                && $command->metadata['feedback'] === null
                && $command->metadata['company_name'] === null
                && $command->metadata['status'] === null
                && $command->metadata['type'] === null
                && $command->metadata['purpose'] === null
                && $command->metadata['staff_name'] === null
                && $command->metadata['notes'] === null
                && $command->metadata['department'] === null
                && $command->metadata['more_phone_numbers'] === null
                && $command->metadata['agency'] === null
        );
    }

    /**
     * @test
     */
    public function it_should_process_international_phone_numbers_correctly(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'International User',
            'phone_number' => '+************', // Indian number
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [],
        ]);

        Log::shouldReceive('info')->times(2);

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->handle();

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Completed, $apiRequest->status);

        Mediator::assertDispatched(
            CreateEnquiry::class,
            static fn (CreateEnquiry $command) => $command->contact->phoneNumber->toPhoneNumber() === '************'
                && $command->contact->phoneNumber->countryCode === '91'
                && $command->contact->phoneNumber->nationalNumber === '9876543210'
        );
    }

    /**
     * @test
     */
    public function it_should_have_without_overlapping_middleware(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'Test User',
            'phone_number' => '+971551234567',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [],
        ]);
        
        $job = new ProcessLead($apiRequest->id);

        // Act
        $middleware = $job->middleware();

        // Assert
        $this->assertIsArray($middleware);
        $this->assertCount(1, $middleware);
        $this->assertInstanceOf(WithoutOverlapping::class, $middleware[0]);
        
        // Check that the key contains vendor_id and phone_number
        $reflection = new \ReflectionClass($middleware[0]);
        $keyProperty = $reflection->getProperty('key');
        $keyProperty->setAccessible(true);
        $key = $keyProperty->getValue($middleware[0]);
        
        $this->assertEquals("vendor:1:phone:+971551234567", $key);
    }

    /**
     * @test
     */
    public function it_should_return_empty_middleware_array_when_api_request_not_found(): void
    {
        // Arrange
        $job = new ProcessLead(999999); // Non-existent ID

        // Act
        $middleware = $job->middleware();

        // Assert
        $this->assertIsArray($middleware);
        $this->assertEmpty($middleware);
    }

    /**
     * @test
     */
    public function it_should_configure_without_overlapping_middleware_correctly(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 2,
            'name' => 'Test User',
            'phone_number' => '+971559876543',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [],
        ]);
        
        $job = new ProcessLead($apiRequest->id);

        // Act
        $middleware = $job->middleware()[0];

        // Assert
        $reflection = new \ReflectionClass($middleware);
        
        // Check releaseAfter
        $releaseAfterProperty = $reflection->getProperty('releaseAfter');
        $releaseAfterProperty->setAccessible(true);
        $this->assertEquals(60, $releaseAfterProperty->getValue($middleware));
        
        // Check expiresAfter
        $expiresAfterProperty = $reflection->getProperty('expiresAfter');
        $expiresAfterProperty->setAccessible(true);
        $this->assertEquals(300, $expiresAfterProperty->getValue($middleware));
        
        // Check that dontRelease was called (releaseAfter should be null)
        $this->assertNull($releaseAfterProperty->getValue($middleware));
    }

    /**
     * @test
     */
    public function it_should_preserve_additional_fields_in_metadata(): void
    {
        // Arrange
        $apiRequest = ApiRequest::create([
            'vendor_id' => 1,
            'name' => 'Custom Fields User',
            'phone_number' => '+971554444444',
            'by' => 1,
            'status' => RequestStatus::Pending,
            'payload' => [
                'email' => '<EMAIL>',
                'feedback' => 'Test feedback',
                'custom_field_1' => 'Value 1',
                'custom_field_2' => 'Value 2',
                'another_field' => 'Another Value',
            ],
        ]);

        Log::shouldReceive('info')->times(2);

        // Act
        $job = new ProcessLead($apiRequest->id);
        $job->handle();

        // Assert
        $apiRequest->refresh();
        $this->assertEquals(RequestStatus::Completed, $apiRequest->status);

        Mediator::assertDispatched(
            CreateEnquiry::class,
            static fn (CreateEnquiry $command) => $command->metadata['custom_field_1'] === 'Value 1'
                && $command->metadata['custom_field_2'] === 'Value 2'
                && $command->metadata['another_field'] === 'Another Value'
                && $command->metadata['email'] === '<EMAIL>'
        );
    }
}

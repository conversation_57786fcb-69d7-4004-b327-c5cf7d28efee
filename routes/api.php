<?php

declare(strict_types=1);

use App\Common\Common;
use App\GlApi\Http\Middlewares\ValidateApiToken;
use App\Http\Controllers\BackendController\AgencyApiController;
use App\Http\Controllers\CallLogController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get(
    'roadroute',
    static fn (Request $request) => Common::getRoadRoute(
        $request->lat1,
        $request->lon1,
        $request->lat2,
        $request->lon2,
        'K'
    )
);

Route::get('/ivr-routing', 'User\IvrController@routing');
Route::post('/ivr-routing', 'User\IvrController@routing');
Route::post('/ivr-call-verify', 'User\IvrController@call_verify');
Route::group([
    'namespace' => 'Api\Android',
    'prefix' => 'application',
], static function ($router): void {

    Route::post('login', 'AuthController@login');

    Route::group([
        'middleware' => 'jwt.auth',
    ], static function ($router): void {

        Route::post('logout', 'AuthController@logout');
        Route::post('refresh', 'AuthController@refresh');
        Route::get('me', 'AuthController@me');
        Route::post('update-profile', 'HomeController@updateProfile');
        Route::post('update-location', 'HomeController@setLocation');

        Route::get('enquiry-types', 'LeadController@getEnquiryTypes');
        Route::get('enquiry-status', 'LeadController@getEnquiryStatus');
        Route::get('sms-templates', 'LeadController@getSmsTemplates');

        Route::resource('leads', 'LeadController');

        Route::get('total-leads-count', 'LeadController@getTotalLeadsCount');
        Route::get('new-leads-count', 'LeadController@newLeadsCount');

        Route::post('update-lead-status/{lead_id}', 'TimeLineController@updateLeadStatus');
        Route::post('add-notes/{lead_id}', 'TimeLineController@addNotes');
        Route::post('add-logs/{lead_id}', 'TimeLineController@addLogs');
        Route::post('add-tasks/{lead_id}', 'TimeLineController@addTask');
        Route::post('add-schedule/{lead_id}', 'TimeLineController@addSchedule');
        Route::get('get-staff', 'TimeLineController@getStaff');
        Route::get('get-calendar-data', 'TimeLineController@getCalendarData');

    });
});

//List Countries
Route::get('/countries', 'BackendController\CountryController@countriesApi');
Route::get('/testimonial', 'BackendController\TestimonialsController@testimonial');

//generate Virtual number based api triggering

Route::resource('/otp-mobile', 'FrontendController\OtpMobileQueueController');
Route::get('/gl-add-number', 'BackendController\GlVerifyController@glVerifyApi');
Route::get('/gl-verify', 'BackendController\GlVerifyController@checkGlVerifyNumber');
Route::get('/gl-verify-status', 'BackendController\GlVerifyController@checkGlVerifyStatus');
Route::get('/glpromo-generate', 'Guest\GlPromoController@index');
//Gdeal
Route::get('/glpromo', 'Api\GLPromoController@index');
//-----
Route::get('/pushsms', 'BackendController\SmsApiController@pushSms');

// ---------start website contact api's------------------- //
//Route::middleware(['custom.cors'])->group(
//    static function (): void {
//        Route::get('/gl-website-contacts', 'User\WebsiteContactController@websiteContactApi')->middleware(ValidateApiToken::class);
//        Route::post('/gl-website-contacts', 'User\WebsiteContactController@websiteContactApi')->middleware(ValidateApiToken::class);
//    }
//);

Route::post('/gl-status-change-api', 'User\WebsiteContactController@statusChangeApi');
Route::post('/gl-task-creation-api', 'User\WebsiteContactController@taskCreationApi');
Route::post('/gl-deal-creation-api', 'User\WebsiteContactController@dealCreationApi');
Route::post('/gl-department-vise-agent-assign/{token}', 'User\WebsiteContactController@assignAgentViseDepartment');
Route::post('/gl-lead-update', 'User\WebsiteContactController@leadUpdate');

Route::get('verify-email/{id}', 'Auth\RegisterController@verifyEmail');

/*-------------------------DYNAMIC MESSAGING---------------------------------------------------*/
Route::post('/callcenter-outgoing-bridging', 'Guest\IVRController@outgoing');
Route::post('/callcentertransferbridging', 'Guest\IVRController@transfer');
Route::post('/call-center-agent-transfer-bridging', 'Guest\IVRController@agentTransfer');
//
//Virtual Number Expiry Date Notification
Route::get('virtual-number-notifications', 'Api\VirtualNumberController@notifications');
//Get Admin
Route::get('get-admin', 'Api\AdminController@getAdmin');
Route::get('get-users', 'Api\AdminController@getUsers');
Route::get('get-staffs/{id}', 'Api\AdminController@getStaffs');

//Enquiry Report

Route::get('monthly-enquiry/{id}', 'Api\EnquiryController@monthlyEnquiry');
Route::get('weekly-enquiry/{id}', 'Api\EnquiryController@weeklyEnquiry');
Route::get('daily-enquiry/{id}', 'Api\EnquiryController@dailyEnquiry');
Route::get('daily-closed-enquiry/{id}', 'Api\EnquiryController@dailyClosedEnquiry');
Route::get('inactive-enquiry/{id}', 'Api\EnquiryController@inactiveEnquiry');
Route::get('enquiry-count/{id}', 'Api\EnquiryController@enquiryCount');

/** Subscription Notification Controller**/

Route::get('email-subscription-messages', 'Api\SubscriptionNotificationController@sendEmailNotifications');
Route::get('telegram-subscription-messages', 'Api\SubscriptionNotificationController@sendTelegramNotifications');
Route::get('sms-subscription-messages', 'Api\SubscriptionNotificationController@sendEmailNotifications');

/** Developer Api's**/

Route::get('push-otp', 'Api\GlOtpController@pushOtp');
Route::post('push-otp', 'Api\GlOtpController@pushOtp');

//Approval Reminder

Route::get('/senderid-approval-reminder', 'Api\ApprovalController@senderid');
Route::get('/mail-configuration-approval-reminder', 'Api\ApprovalController@mailConfiguration');
Route::get('/mail-template-approval-reminder', 'Api\ApprovalController@mailTemplate');
Route::get('/default-senderid-approval-reminder', 'Api\ApprovalController@defaultSenderid');
Route::get('/api-template-approval-reminder', 'Api\ApprovalController@apiTemplate');
Route::get('/whatsapp-template-approval-reminder', 'Api\ApprovalController@whatsappTemplate');
Route::get('/sms-template-approval-reminder', 'Api\ApprovalController@smsTemplate');
Route::get('/mail-senderid-approval-reminder', 'Api\ApprovalController@mailSenderid');

/** send message through telegram */
Route::post('/telegram/send-message', 'SendTelegramMessageController@sendTelegramMessage');

//Telegram Webhook
Route::post('telegram-webhook', 'TelegramWebhookController@index');

Route::get('get-lead-autocomplete', 'BackendController\EnquiryController@searchAutocompleteEnquiry');

/**
 * Bonvoice integration services
 */
Route::post(
    'calls/bonvoice',
    'Guest\BonvoiceController@bonvoiceCallResponse'
)->name('call-response-fetching.index');
Route::post('call-voxbay', 'Guest\VoxbayController@clickToCallVoxbay');
Route::post('call-voxbayX', 'Guest\VoxbayController@clickToCallVoxbayX');
Route::post('call-bonvoice', 'Guest\BonvoiceController@clickToCallBonvoice');
/* End services */

Route::get('list-crm-users', 'BackendController\ConnectionApiController@listCrmUsers');
Route::post('list-crm-user-details', 'BackendController\ConnectionApiController@UserDetails');
Route::post('list-users-with-plan', 'BackendController\ConnectionApiController@userWithPlan');
Route::post('plan-insertion-from-billing', 'BackendController\ConnectionApiController@subscriptionSaveToCrm');
Route::post('subscribe-user-plan', 'BackendController\ConnectionApiController@subscribeUserPlan');
Route::post(
    'delete-from-billing-subscription',
    'BackendController\ConnectionApiController@deleteFromBillingSubscription'
);
Route::post('get-active-user-count', 'BackendController\ConnectionApiController@getActiveUserCount');
Route::post('license-count-update', 'BackendController\ConnectionApiController@licenseCountUpdate');
Route::post('update-password', 'BackendController\ConnectionApiController@updatePassword');

// list user details in billing
Route::post('get-user-details-to-billing', 'BackendController\UserController@getUserDetailsToBilling');

// Alukkas
Route::post('get-agents', 'BackendController\ConnectionApiController@getAgents');
Route::post('get-reports', 'BackendController\ConnectionApiController@getreports');

// Facebook webhook callback
Route::get(
    'webhook/facebook/get-call-back/{token?}',
    'BackendController\ConnectionApiController@getFacebookCallBack'
);

// Partner portal api
Route::post('get-lead-status', 'BackendController\ConnectionApiController@getLeadStatus');
Route::post('fetch-lead-status-list', 'BackendController\ConnectionApiController@fetchLeadStatus');

/* ---------- call logs api. sync call logs from dialer app */
Route::prefix('call-logs')->controller(CallLogController::class)->group(
    static function (): void {
        Route::post('handlewebhook', 'handleWebhook');
        Route::post('push-recordings', 'pushRecordings');
        Route::post('get-lead-name', 'getLeadName');
    }
);
Route::prefix('agency')->controller(AgencyApiController::class)->group(static function (): void {
    Route::post('lead-list', 'leadLlist');
    Route::post('lead-status-count', 'leadStatusCount');
    Route::post('lead-delete', 'agencyLeadDelete');
});

require 'api_agentapp.php';
require 'api_userapp.php';

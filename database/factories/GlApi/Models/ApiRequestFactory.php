<?php

declare(strict_types=1);

namespace Database\Factories\GlApi\Models;

use App\GlApi\Enums\RequestStatus;
use App\GlApi\Enums\RequestType;
use App\GlApi\Models\ApiRequest;
use App\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Tests\FakePhoneNumber;

final class ApiRequestFactory extends Factory
{
    protected $model = ApiRequest::class;

    public function definition(): array
    {
        $phoneNumber = FakePhoneNumber::create('IN');

        return [
            'vendor_id' => $this->faker->numberBetween(1, 100),
            'by' => User::factory(),
            'name' => $this->faker->name(),
            'phone_number' => $phoneNumber->toPhoneNumber(),
            'status' => RequestStatus::Pending,
            'type' => RequestType::NewLead,
            'payload' => json_encode([
                'email' => $this->faker->safeEmail(),
                'requirement' => $this->faker->sentence(),
                'city' => $this->faker->city(),
                'state' => $this->faker->state(),
                'source' => $this->faker->randomElement(['Website', 'Phone', 'Walk-in', 'Referral']),
            ]),
            'failure_reason' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Configure the factory for in-progress status.
     */
    public function inProgress(): self
    {
        return $this->state(static fn (array $attributes) => [
            'status' => RequestStatus::InProgress,
        ]);
    }

    /**
     * Configure the factory for completed status.
     */
    public function completed(): self
    {
        return $this->state(static fn (array $attributes) => [
            'status' => RequestStatus::Completed,
        ]);
    }

    /**
     * Configure the factory for failed status.
     */
    public function failed(
        string $reason = 'Unknown error'
    ): self {
        return $this->state(static fn (array $attributes) => [
            'status' => RequestStatus::Failed,
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Configure the factory for a specific vendor.
     */
    public function forVendor(int $vendorId): self
    {
        return $this->state(static fn (array $attributes) => [
            'vendor_id' => $vendorId,
        ]);
    }

    /**
     * Configure the factory for a specific user.
     */
    public function byUser(int $userId): self
    {
        return $this->state(static fn (array $attributes) => [
            'by' => $userId,
        ]);
    }

    /**
     * Configure the factory with custom payload.
     */
    public function withPayload(array $payload): self
    {
        return $this->state(static fn (array $attributes) => [
            'payload' => json_encode($payload),
        ]);
    }

    /**
     * Configure the factory with specific phone number.
     */
    public function withPhoneNumber(
        string $phoneNumber
    ): self {
        return $this->state(static fn (array $attributes) => [
            'phone_number' => $phoneNumber,
        ]);
    }
}
